// routes/nistRoutes.js
const express = require('express');
const axios = require('axios');
const router = express.Router();

// NIST NVD API base URL
const NIST_BASE_URL = 'https://services.nvd.nist.gov/rest/json/cves/2.0';

// @route   GET /api/nist/test
// @desc    Test endpoint to verify NIST routes are working
// @access  Public (no auth required)
router.get('/test', (req, res) => {
  console.log('[NIST Proxy] Test endpoint called - routes are working!');
  res.json({
    success: true,
    message: 'NIST proxy routes are working',
    timestamp: new Date().toISOString(),
    baseUrl: NIST_BASE_URL
  });
});

// @route   GET /api/nist/search
// @desc    Proxy NIST NVD vulnerability search API
// @access  Public
router.get('/search', async (req, res) => {
  try {
    console.log('[NIST Proxy] Vulnerability search request:', req.query);

    // Forward all query parameters to NIST API
    const response = await axios.get(NIST_BASE_URL, {
      params: req.query,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
      },
      timeout: 30000 // 30 second timeout for NIST API
    });

    console.log('[NIST Proxy] Vulnerability search response:', {
      status: response.status,
      totalResults: response.data?.totalResults || 0,
      vulnerabilitiesCount: response.data?.vulnerabilities?.length || 0
    });

    res.json(response.data);
  } catch (error) {
    console.error('[NIST Proxy] Vulnerability search error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url
    });

    // Handle rate limiting specifically
    if (error.response?.status === 403) {
      return res.status(429).json({
        error: 'NIST API rate limit exceeded',
        message: 'Please wait before making another request',
        status: 429,
        retryAfter: 30
      });
    }

    res.status(error.response?.status || 500).json({
      error: 'NIST API request failed',
      message: error.message,
      status: error.response?.status || 500
    });
  }
});

// @route   GET /api/nist/cpe-search
// @desc    Proxy NIST CPE search API
// @access  Public
router.get('/cpe-search', async (req, res) => {
  try {
    console.log('[NIST Proxy] CPE search request:', req.query);

    const response = await axios.get(NIST_BASE_URL, {
      params: req.query,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
      },
      timeout: 30000
    });

    console.log('[NIST Proxy] CPE search response:', {
      status: response.status,
      totalResults: response.data?.totalResults || 0,
      vulnerabilitiesCount: response.data?.vulnerabilities?.length || 0
    });

    res.json(response.data);
  } catch (error) {
    console.error('[NIST Proxy] CPE search error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText
    });

    res.status(error.response?.status || 500).json({
      error: 'NIST CPE search failed',
      message: error.message,
      status: error.response?.status || 500
    });
  }
});

// @route   GET /api/nist/keyword-search
// @desc    Proxy NIST keyword search API
// @access  Public
router.get('/keyword-search', async (req, res) => {
  try {
    console.log('[NIST Proxy] Keyword search request:', req.query);

    const response = await axios.get(NIST_BASE_URL, {
      params: req.query,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
      },
      timeout: 30000
    });

    console.log('[NIST Proxy] Keyword search response:', {
      status: response.status,
      totalResults: response.data?.totalResults || 0,
      vulnerabilitiesCount: response.data?.vulnerabilities?.length || 0
    });

    res.json(response.data);
  } catch (error) {
    console.error('[NIST Proxy] Keyword search error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText
    });

    res.status(error.response?.status || 500).json({
      error: 'NIST keyword search failed',
      message: error.message,
      status: error.response?.status || 500
    });
  }
});

module.exports = router;
